<package>
  <name>hector_quadrotor</name>
  <version>0.3.5</version>
  <description>hector_quadrotor contains packages related to modeling, control and simulation of quadrotor UAV systems</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://ros.org/wiki/hector_quadrotor</url>
  <url type="bugtracker">https://github.com/tu-darmstadt-ros-pkg/hector_quadrotor/issues</url>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <!-- Dependencies which this package needs to build itself. -->
  <buildtool_depend>catkin</buildtool_depend>

  <!-- Dependencies needed to compile this package. -->


  <!-- Dependencies needed after this package is compiled. -->
  <run_depend>hector_quadrotor_controllers</run_depend>
  <run_depend>hector_quadrotor_description</run_depend>
  <run_depend>hector_quadrotor_model</run_depend>
  <run_depend>hector_quadrotor_teleop</run_depend>
  <run_depend>hector_uav_msgs</run_depend>

  <!-- Dependencies needed only for running tests. -->

</package>
