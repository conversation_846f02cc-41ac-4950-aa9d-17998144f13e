<?xml version="1.0"?>
<package format="2">
  <name>asv_wave_sim_gazebo</name>
  <version>0.1.2</version>
  <description>
    This package contains Gazebo media, models and worlds for simulating
    water waves and dynamics for surface vessels. There are ROS 
    launch scripts that may be used to launch a Gazebo session and
    load a world and models using `roslaunch`. 
  </description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>GPLv3</license>
  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>catkin</buildtool_depend>


 <build_depend>message_generation</build_depend>
  <exec_depend>message_runtime</exec_depend>
  <build_depend>std_msgs</build_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>asv_wave_sim_gazebo_plugins</exec_depend>
  <exec_depend>gazebo_ros</exec_depend>

  <export>
    <gazebo_ros gazebo_media_path="${prefix}" />
    <gazebo_ros gazebo_model_path="${prefix}/world_models:${prefix}/models" />
  </export>
  
  
  
</package>
