<package>
  <name>hector_quadrotor_gazebo</name>
  <version>0.3.5</version>
  <description>hector_quadrotor_gazebo provides a quadrotor model for the gazebo simulator.
     It can be commanded using geometry_msgs/Twist messages.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://ros.org/wiki/hector_quadrotor_gazebo</url>
  <url type="bugtracker">https://github.com/tu-darmstadt-ros-pkg/hector_quadrotor/issues</url>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <!-- Dependencies which this package needs to build itself. -->
  <buildtool_depend>catkin</buildtool_depend>

  <!-- Dependencies needed to compile this package. -->
  <!-- required to check available plugins -->
  <build_depend>hector_quadrotor_gazebo_plugins</build_depend>
  <build_depend>hector_quadrotor_controller_gazebo</build_depend>

  <!-- Dependencies needed after this package is compiled. -->
  <run_depend>gazebo_plugins</run_depend>
  <run_depend>hector_gazebo_plugins</run_depend>
  <run_depend>hector_sensors_gazebo</run_depend>
  <run_depend>hector_quadrotor_actions</run_depend>
  <run_depend>hector_quadrotor_description</run_depend>
  <run_depend>hector_quadrotor_model</run_depend>
  <run_depend>hector_quadrotor_gazebo_plugins</run_depend>
  <run_depend>hector_quadrotor_controller_gazebo</run_depend>
  <run_depend>hector_quadrotor_controllers</run_depend>
  <run_depend>hector_quadrotor_pose_estimation</run_depend>
  <run_depend>hector_quadrotor_teleop</run_depend>
  <run_depend>hector_uav_msgs</run_depend>
  <run_depend>message_to_tf</run_depend>
  <run_depend>robot_state_publisher</run_depend>

</package>
