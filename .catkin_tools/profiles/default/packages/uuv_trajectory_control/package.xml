<?xml version="1.0"?>
<package format="2">
  <name>uuv_trajectory_control</name>
  <version>0.6.13</version>
  <description>The uuv_trajectory_control package</description>

  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON> More<PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>Apache-2.0</license>

  <buildtool_depend>catkin</buildtool_depend>

  <exec_depend>rospy</exec_depend>
  <exec_depend>roslib</exec_depend>
  <exec_depend>python-numpy</exec_depend>
  <exec_depend>python-matplotlib</exec_depend>
  <exec_depend>python-yaml</exec_depend>
  <exec_depend>python-scipy</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>uuv_control_msgs</exec_depend>
  <exec_depend>tf</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>visualization_msgs</exec_depend>
  <exec_depend>uuv_assistants</exec_depend>

  <test_depend>rosunit</test_depend>
  <test_depend>rostest</test_depend>
  <export>
  </export>
</package>
