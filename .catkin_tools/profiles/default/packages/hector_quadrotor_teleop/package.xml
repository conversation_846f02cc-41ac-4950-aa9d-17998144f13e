<package>
  <name>hector_quadrotor_teleop</name>
  <version>0.3.5</version>
  <description>hector_quadrotor_teleop enables quadrotor flying with a joystick by
     processing joy/Joy messages and translating them to geometry_msgs/Twist.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://ros.org/wiki/hector_quadrotor_teleop</url>
  <url type="bugtracker">https://github.com/tu-darmstadt-ros-pkg/hector_quadrotor/issues</url>

  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>actionlib</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>hector_quadrotor_interface</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>hector_uav_msgs</build_depend>
  <build_depend>tf2_geometry_msgs</build_depend>

  <run_depend>actionlib</run_depend>
  <run_depend>geometry_msgs</run_depend>
  <run_depend>hector_uav_msgs</run_depend>
  <run_depend>hector_quadrotor_interface</run_depend>
  <run_depend>joy</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>sensor_msgs</run_depend>
  <run_depend>tf2_geometry_msgs</run_depend>


</package>
