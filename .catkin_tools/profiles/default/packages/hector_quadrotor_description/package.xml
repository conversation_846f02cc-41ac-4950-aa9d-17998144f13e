<package>
  <name>hector_quadrotor_description</name>
  <version>0.3.5</version>
  <description>hector_quadrotor_description provides an URDF model of a quadrotor UAV.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://ros.org/wiki/hector_quadrotor_description</url>
  <url type="bugtracker">https://github.com/tu-darmstadt-ros-pkg/hector_quadrotor/issues</url>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <!-- Dependencies which this package needs to build itself. -->
  <buildtool_depend>catkin</buildtool_depend>

  <!-- Dependencies needed to compile this package. -->

  <!-- Dependencies needed after this package is compiled. -->
  <run_depend>hector_sensors_description</run_depend>

  <!-- Dependencies needed only for running tests. -->

</package>
