<?xml version="1.0"?>
<package>
  <name>hector_quadrotor_actions</name>
  <version>0.0.0</version>
  <description>The hector_quadrotor_actions package provides action server implementations for some complex behaviors of the quadrotor.</description>
  <maintainer email="johanne<PERSON>@intermodalics.eu"><PERSON></maintainer>

  <license>BSD</license>

  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>actionlib</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>hector_quadrotor_interface</build_depend>
  <build_depend>hector_uav_msgs</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>tf2</build_depend>
  <build_depend>tf2_geometry_msgs</build_depend>

  <run_depend>actionlib</run_depend>
  <run_depend>geometry_msgs</run_depend>
  <run_depend>hector_quadrotor_interface</run_depend>
  <run_depend>hector_uav_msgs</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>tf2</run_depend>
  <run_depend>tf2_geometry_msgs</run_depend>

</package>
