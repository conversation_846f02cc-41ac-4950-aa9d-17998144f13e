<package>
  <name>hector_quadrotor_controllers</name>
  <version>0.0.0</version>
  <description>hector_quadrotor_controllers provides controller plugins for quadrotor control using <a href="http://wiki.ros.org/ros_control">ros_control</a>.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://ros.org/wiki/hector_quadrotor_controller</url>
  <url type="bugtracker">https://github.com/tu-darmstadt-ros-pkg/hector_quadrotor/issues</url>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend version_gte="1.14.0">control_toolbox</build_depend>
  <build_depend>controller_interface</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>hardware_interface</build_depend>
  <build_depend>hector_quadrotor_interface</build_depend>
  <build_depend>hector_uav_msgs</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>tf</build_depend>
  <build_depend>tf2_geometry_msgs</build_depend>
  <build_depend>topic_tools</build_depend>
  <build_depend>visualization_msgs</build_depend>

  <run_depend version_gte="1.14.0">control_toolbox</run_depend>
  <run_depend>controller_interface</run_depend>
  <run_depend>geometry_msgs</run_depend>
  <run_depend>hardware_interface</run_depend>
  <run_depend>hector_quadrotor_interface</run_depend>
  <run_depend>hector_uav_msgs</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>tf</run_depend>
  <run_depend>tf2_geometry_msgs</run_depend>
  <run_depend>topic_tools</run_depend>
  <run_depend>visualization_msgs</run_depend>

  <export>
    <controller_interface plugin="${prefix}/plugin.xml"/>
  </export>

</package>
