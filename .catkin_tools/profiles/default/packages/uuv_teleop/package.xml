<?xml version="1.0"?>
<package format="2">
  <name>uuv_teleop</name>
  <version>0.6.13</version>
  <description>ROS nodes to generate command topics for vehicles and manipulators using a joystick input</description>

  <maintainer email="<EMAIL>"><PERSON> Morena <PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON><PERSON></author>

  <license>Apache-2.0</license>

  <buildtool_depend>catkin</buildtool_depend>

  <exec_depend>joy</exec_depend>
  <exec_depend>joy_teleop</exec_depend>
  <exec_depend>python-numpy</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>tf</exec_depend>
  <exec_depend>uuv_gazebo_ros_plugins_msgs</exec_depend>
  <exec_depend>uuv_thruster_manager</exec_depend>
</package>
