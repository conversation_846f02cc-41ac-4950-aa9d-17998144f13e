<package>
  <name>hector_quadrotor_model</name>
  <version>0.3.5</version>
  <description>hector_quadrotor_model provides libraries that model several aspects of quadrotor dynamics.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://ros.org/wiki/hector_quadrotor_model</url>
  <url type="bugtracker">https://github.com/tu-darmstadt-ros-pkg/hector_quadrotor/issues</url>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <!-- Dependencies which this package needs to build itself. -->
  <buildtool_depend>catkin</buildtool_depend>

  <!-- Dependencies needed to compile this package. -->
  <build_depend>geometry_msgs</build_depend>
  <build_depend>hector_uav_msgs</build_depend>
  <build_depend>eigen</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>cmake_modules</build_depend>
  <build_depend>boost</build_depend>

  <!-- Dependencies needed after this package is compiled. -->
  <run_depend>geometry_msgs</run_depend>
  <run_depend>hector_uav_msgs</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>boost</run_depend>

  <!-- Dependencies needed only for running tests. -->

</package>
