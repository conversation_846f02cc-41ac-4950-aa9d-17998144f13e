<package>
  <name>hector_uav_msgs</name>
  <version>0.3.5</version>
  <description>hector_uav_msgs is a message package that contains messages for UAV controller inputs and outputs and some sensor readings not covered by sensor_msgs.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://ros.org/wiki/hector_uav_msgs</url>
  <url type="bugtracker">https://github.com/tu-darmstadt-ros-pkg/hector_quadrotor/issues</url>

  <author email="<EMAIL>"><PERSON></author>

  <!-- Dependencies which this package needs to build itself. -->
  <buildtool_depend>catkin</buildtool_depend>

  <!-- Dependencies needed to compile this package. -->
  <build_depend>actionlib_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>message_generation</build_depend>
  <build_depend>std_msgs</build_depend>

  <!-- Dependencies needed after this package is compiled. -->
  <run_depend>actionlib_msgs</run_depend>
  <run_depend>geometry_msgs</run_depend>
  <run_depend>message_runtime</run_depend>
  <run_depend>std_msgs</run_depend>

  <!-- Dependencies needed only for running tests. -->

</package>
