<?xml version="1.0"?>
<package>
  <name>hector_quadrotor_pose_estimation</name>
  <version>0.3.5</version>
  <description>
    hector_quadrotor_pose_estimation provides a <a href="http://wiki.ros.org/hector_pose_estimation">hector_pose_estimation</a> node and nodelet specialized for hector_quadrotor.
  </description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://ros.org/wiki/hector_quadrotor_pose_estimation</url>

  <author email="<EMAIL>"><PERSON></author>

  <!-- Dependencies which this package needs to build itself. -->
  <buildtool_depend>catkin</buildtool_depend>

  <!-- Dependencies needed to compile this package. -->
  <build_depend version_gte="0.2.0">hector_pose_estimation</build_depend>
  <build_depend>hector_uav_msgs</build_depend>

  <!-- Dependencies needed after this package is compiled. -->
  <run_depend version_gte="0.2.0">hector_pose_estimation</run_depend>
  <run_depend>hector_uav_msgs</run_depend>
  <run_depend>nodelet</run_depend>

  <!-- Dependencies needed only for running tests. -->

  <export>
    <nodelet plugin="${prefix}/hector_quadrotor_pose_estimation_nodelets.xml"/>
  </export>

</package>
