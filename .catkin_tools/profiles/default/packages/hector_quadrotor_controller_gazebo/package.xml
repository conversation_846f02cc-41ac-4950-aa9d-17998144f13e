<?xml version="1.0"?>
<package>
  <name>hector_quadrotor_controller_gazebo</name>
  <version>0.3.5</version>
  <description>The hector_quadrotor_controller_gazebo package implements the ros_control RobotHWSim interface for the quadrotor controller in package hector_quadrotor_controller.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://ros.org/wiki/hector_quadrotor_controller_gazebo</url>
  <url type="bugtracker">https://github.com/tu-darmstadt-ros-pkg/hector_quadrotor/issues</url>

  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>gazebo_dev</build_depend>
  <build_depend version_gte="2.3.4">gazebo_ros_control</build_depend>
  <build_depend>hector_quadrotor_interface</build_depend>
  <run_depend>gazebo</run_depend>
  <run_depend version_gte="2.3.4">gazebo_ros_control</run_depend>
  <run_depend>hector_quadrotor_interface</run_depend>

  <export>
    <gazebo_ros_control plugin="${prefix}/quadrotor_controller_gazebo.xml"/>
  </export>
</package>
