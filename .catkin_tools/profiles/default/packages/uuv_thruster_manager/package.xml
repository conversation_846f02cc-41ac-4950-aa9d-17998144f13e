<?xml version="1.0"?>
<package format="2">
  <name>uuv_thruster_manager</name>
  <version>0.6.13</version>
  <description>The thruster manager package</description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>

  <author email="<EMAIL>">Musa Morena <PERSON></author>
  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON><PERSON></author>

  <license>Apache-2.0</license>

  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>message_generation</build_depend>

  <depend>std_msgs</depend>
  
  <exec_depend>message_runtime</exec_depend>
  <exec_depend>tf</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>python-yaml</exec_depend>
  <exec_depend>uuv_gazebo_ros_plugins_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>uuv_assistants</exec_depend>

  <test_depend>rosunit</test_depend>
  <test_depend>rostest</test_depend>
  <test_depend>python-numpy</test_depend>
  <test_depend>uuv_assistants</test_depend>
  <test_depend>geometry_msgs</test_depend>
  <test_depend>joint_state_publisher</test_depend>
  <test_depend>robot_state_publisher</test_depend>
  <test_depend>xacro</test_depend>

</package>

