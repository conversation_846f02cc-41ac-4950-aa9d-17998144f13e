<?xml version="1.0"?>
<package format="3">
  <name>robot_localization</name>
  <version>2.7.7</version>
  <description>Provides nonlinear state estimation through sensor fusion of an abritrary number of sensors.</description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://ros.org/wiki/robot_localization</url>

  <author email="<EMAIL>"><PERSON></author> 

  <buildtool_depend>catkin</buildtool_depend>

  <depend>angles</depend>
  <depend>cmake_modules</depend>
  <depend>diagnostic_msgs</depend>
  <depend>diagnostic_updater</depend>
  <depend>eigen</depend>
  <depend>eigen_conversions</depend>
  <depend>geographiclib</depend>
  <depend>geographic_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>message_filters</depend>
  <depend>nav_msgs</depend>
  <depend>nodelet</depend>
  <depend>roscpp</depend>
  <depend>sensor_msgs</depend>
  <depend>std_msgs</depend>
  <depend>std_srvs</depend>
  <depend>tf2</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>tf2_ros</depend>
  <depend>yaml-cpp</depend>

  <build_depend>message_generation</build_depend>
  <build_depend condition="$ROS_PYTHON_VERSION == 2">python-catkin-pkg</build_depend>
  <build_depend condition="$ROS_PYTHON_VERSION == 3">python3-catkin-pkg</build_depend>
  <build_depend>roslint</build_depend>

  <exec_depend>message_runtime</exec_depend>

  <test_depend>rosbag</test_depend>
  <test_depend>rostest</test_depend>
  <test_depend>rosunit</test_depend>

  <export>
    <rosdoc config="rosdoc.yaml" />
    <nodelet plugin="${prefix}/nodelet_plugins.xml" />
  </export>

</package>
