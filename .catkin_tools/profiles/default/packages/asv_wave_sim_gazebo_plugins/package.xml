<?xml version="1.0"?>
<package format="2">
  <name>asv_wave_sim_gazebo_plugins</name>
  <version>0.1.2</version>
  <description>
    This package contains Gazebo plugins for the simulation of 
    water surface waves and hydrostatic and hydrodynamics forces. 
  </description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>GPLv3</license>

  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>cgal</build_depend>
  <build_depend>eigen</build_depend>
  <build_depend>gazebo_ros</build_depend>
  
  <exec_depend>cgal</exec_depend>
  <exec_depend>gazebo_ros</exec_depend>
  

   <depend>tf</depend>
  <depend>nav_msgs</depend>
  <depend>gazebo_msgs</depend>
  <depend>rosgraph_msgs</depend>
  <depend>roscpp</depend>


  <export>
    <gazebo_ros plugin_path="${prefix}/lib"/>
  </export>
</package>
