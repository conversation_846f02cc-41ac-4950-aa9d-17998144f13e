# Système de Navigation Autonome et Collecte de Déchets IA

Ce système coordonne la navigation autonome et la collecte de déchets par IA pour un véhicule de surface autonome (ASV).

## Architecture du Système

### Nœuds Principaux

1. **`navigation_node.py`** - Navigation autonome continue
2. **`ai_waste_collector.py`** - Détection et collecte de déchets par IA

### Communication ROS

#### Topics de Coordination
- `/navigation_control` - Contrôle de la navigation (String)
- `/navigation_active` - État de la navigation (Bool)
- `/waste_collection_active` - État de la collecte (Bool)
- `/navigation_status` - Statut de la navigation (String)
- `/collection_status` - Statut de la collecte (String)

#### Topics de Données
- `/waste_detection` - Détections de déchets (WasteDetection)
- `/collection_control` - Contrôle manuel de collecte (String)

## Logique de Fonctionnement

### Mode Autonome
1. **Navigation Active** : Le nœud de navigation exécute des patterns de navigation
2. **Détection de Déchets** : L'IA surveille continuellement les déchets
3. **Activation de Collecte** : Quand des déchets sont détectés :
   - L'IA envoie `pause` à la navigation
   - L'IA prend le contrôle des ventilateurs
   - La collecte commence
4. **Fin de Collecte** : Quand plus de déchets (timeout 5s) :
   - L'IA envoie `activate` à la navigation
   - La navigation reprend

### Mode Manuel
- La navigation est pausée
- La collecte IA est désactivée
- Contrôle manuel via web interface

## Installation et Utilisation

### 1. Rendre les Scripts Exécutables
```bash
cd ~/asv_ws/src/asv_wave_sim/asv_wave_sim_gazebo/scripts/AI_waste_Collection/
chmod +x *.py
```

### 2. Lancer le Système Complet
```bash
# Terminal 1 - Gazebo (si pas déjà lancé)
roslaunch asv_wave_sim_gazebo asv_world.launch

# Terminal 2 - Système IA + Navigation
roslaunch asv_wave_sim_gazebo ai_navigation_system.launch
```

### 3. Lancer les Nœuds Individuellement
```bash
# Navigation autonome seulement
rosrun asv_wave_sim_gazebo navigation_node.py

# Collecte IA seulement
rosrun asv_wave_sim_gazebo ai_waste_collector.py
```

### 4. Tester la Coordination
```bash
# Lancer le script de test
rosrun asv_wave_sim_gazebo test_coordination.py
```

## Monitoring du Système

### Visualiser les Topics
```bash
# Voir tous les topics
rostopic list

# Monitorer l'état de navigation
rostopic echo /navigation_active

# Monitorer l'état de collecte
rostopic echo /waste_collection_active

# Voir les détections de déchets
rostopic echo /waste_detection
```

### Contrôle Manuel

#### Activer/Désactiver la Collecte
```bash
# Démarrer la collecte manuellement
rostopic pub /collection_control std_msgs/String "data: 'start_collection'"

# Arrêter la collecte manuellement
rostopic pub /collection_control std_msgs/String "data: 'end_collection'"
```

#### Contrôler la Navigation
```bash
# Activer la navigation
rostopic pub /navigation_control std_msgs/String "data: 'activate'"

# Pauser la navigation
rostopic pub /navigation_control std_msgs/String "data: 'pause'"
```

## Configuration

### Paramètres de Navigation (`navigation_node.py`)
```python
self.nav_params = {
    'cruise_speed': -0.4,      # Vitesse de croisière
    'turn_speed': -0.3,        # Vitesse de virage
    'pattern_duration': 10.0,  # Durée de chaque pattern
}
```

### Paramètres de Collecte (`ai_waste_collector.py`)
```python
self.fan_params = {
    'forward_speed': -0.8,     # Vitesse d'approche
    'strong_rotation': -0.4,   # Rotation forte
    'gentle_rotation': -0.2,   # Rotation douce
}
self.waste_detection_timeout = 5.0  # Timeout en secondes
```

## Dépannage

### Problèmes Courants

1. **Les nœuds ne communiquent pas**
   - Vérifier que tous les nœuds sont lancés
   - Vérifier les topics avec `rostopic list`

2. **La navigation ne s'arrête pas lors de la détection**
   - Vérifier les logs : `rosnode info ai_waste_collector_node`
   - Vérifier Firebase si utilisé

3. **Les ventilateurs ne répondent pas**
   - Vérifier que Gazebo est lancé
   - Vérifier les services : `rosservice list | grep gazebo`

### Logs et Debug
```bash
# Voir les logs d'un nœud
rosnode info autonomous_navigation_node
rosnode info ai_waste_collector_node

# Activer les logs debug (modifier le code)
logging.basicConfig(level=logging.DEBUG)
```

## Structure des Fichiers

```
AI_waste_Collection/
├── navigation_node.py          # Navigation autonome
├── ai_waste_collector.py       # Collecte IA
├── test_coordination.py        # Tests de coordination
├── web_contro_stream.py        # Interface web (optionnel)
└── README.md                   # Cette documentation
```

## Intégration avec Firebase

Le système utilise Firebase pour la gestion des modes (autonome/manuel). Assurez-vous que :
- Le fichier `credentials/auth.json` existe
- Firebase est configuré correctement
- Les permissions sont appropriées

## Développement et Extension

### Ajouter de Nouveaux Patterns de Navigation
Modifier la méthode `execute_navigation_pattern()` dans `navigation_node.py`

### Modifier la Logique de Collecte
Modifier la méthode `control_boat_movement()` dans `ai_waste_collector.py`

### Ajouter de Nouveaux Topics
Mettre à jour `_setup_ros_communication()` dans les deux nœuds
