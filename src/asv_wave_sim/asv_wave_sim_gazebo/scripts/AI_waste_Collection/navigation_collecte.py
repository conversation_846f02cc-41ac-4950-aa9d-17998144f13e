#!/usr/bin/env python3

import rospy
import time
import actionlib
from move_base_msgs.msg import MoveBaseAction, MoveBaseGoal
from geometry_msgs.msg import Point, Quaternion
from std_msgs.msg import String
from asv_wave_sim_gazebo.msg import WasteDetection
from gazebo_msgs.srv import ApplyBodyWrench, ApplyBodyWrenchRequest, BodyRequest
import firebase_admin
from firebase_admin import db, credentials
import yaml

class NavigationAICollector:
    def __init__(self):
        rospy.init_node('navigation_ai_collector_combined')

        # === Navigation parameters ===
        self.robot_id = rospy.get_param('~robot_id', 'default_robot')
        self.firebase_url = rospy.get_param('~firebase_url', 'https://boatt-217dd-default-rtdb.firebaseio.com')
        self.firebase_cred_path = rospy.get_param('~firebase_cred', '/home/<USER>/asv_ws/credentials/auth.json')
        self.map_yaml_path = rospy.get_param('~map_yaml', '/home/<USER>/asv_ws/src/asv_wave_sim/asv_wave_sim_gazebo/maps/map.yaml')
        self.origin_offset_x, self.origin_offset_y = self.load_origin_offsets(self.map_yaml_path)
        self.init_firebase()
        self.move_base_client = actionlib.SimpleActionClient('move_base', MoveBaseAction)
        rospy.loginfo("Waiting for move_base action server...")
        self.move_base_client.wait_for_server()
        rospy.loginfo("Connected to move_base action server")
        self.navigation_status_pub = rospy.Publisher('/navigation_status', String, queue_size=10)
        self.points = []
        self.current_point_idx = 0
        self.mode = "NAVIGATION"  # NAVIGATION or COLLECT
        self.bottom_middle_timer = None
        self.detection_confirmed = False
        self.detection_counter = 0
        self.detection_threshold = 2  # nombre de détections consécutives pour confirmer
        self.collection_cooldown = 3.0  # secondes d'attente après collecte
        self.last_collection_time = 0

        # === Waste Collector parameters ===
        self.max_torque = 0.20
        self.min_torque = -0.20
        self.base_speed = -0.30
        self.fan_right = "boatcleaningc::fandroit"
        self.fan_left = "boatcleaningc::fangauche"
        self.kp_angular = 1.2
        self.ki_angular = 0.15
        self.kd_angular = 0.25
        self.kp_linear = 0.6
        self.ki_linear = 0.08
        self.kd_linear = 0.12
        self.kp_angular_fine = 0.4
        self.ki_angular_fine = 0.05
        self.kd_angular_fine = 0.08
        self.kp_linear_fine = 0.2
        self.ki_linear_fine = 0.02
        self.kd_linear_fine = 0.04
        self.search_speed = 0.4
        self.approach_speed = 0.15
        self.collection_speed = 0.2
        self.centering_threshold = 0.01
        self.precision_threshold = 0.05
        self.max_error_threshold = 0.1
        self.fine_error_threshold = 0.02
        self.integral_windup_limit = 0.3
        self.derivative_filter_alpha = 0.7
        self.angular_error_integral = 0.0
        self.linear_error_integral = 0.0
        self.previous_angular_error = 0.0
        self.previous_linear_error = 0.0
        self.previous_time = time.time()
        self.angular_error_history = [0.0] * 5
        self.linear_error_history = [0.0] * 5
        self.angular_derivative_filtered = 0.0
        self.linear_derivative_filtered = 0.0
        self.collection_count = 0
        self.regulation_active = False
        self.last_detection_time = time.time()
        self.taux_erreur_period = 3.0
        self.in_search_state = False
        self.search_start_time = None

        # === Gazebo services ===
        rospy.wait_for_service('/gazebo/apply_body_wrench')
        rospy.wait_for_service('/gazebo/clear_body_wrenches')
        self.apply_wrench = rospy.ServiceProxy('/gazebo/apply_body_wrench', ApplyBodyWrench)
        self.clear_wrench = rospy.ServiceProxy('/gazebo/clear_body_wrenches', BodyRequest)

        # === Waste detection state ===
        self.waste_detected = False
        self.last_detection_msg = None

        # === Subscribers ===
        rospy.Subscriber('/waste_detection', WasteDetection, self.waste_detection_callback)
        rospy.Timer(rospy.Duration(0.5), self.taux_erreur_check)

    # --- Navigation methods ---
    def load_origin_offsets(self, yaml_path):
        try:
            with open(yaml_path, 'r') as f:
                map_data = yaml.safe_load(f)
            origin = map_data.get('origin')
            return abs(float(origin[0])), abs(float(origin[1]))
        except Exception as e:
            rospy.logwarn(f"Failed to load map YAML: {e}. Using default offsets.")
            return 75.0, 9.0

    def init_firebase(self):
        try:
            cred = credentials.Certificate(self.firebase_cred_path)
            firebase_admin.initialize_app(cred, {
                'databaseURL': self.firebase_url
            })
            rospy.loginfo("Firebase initialized successfully")
        except Exception as e:
            rospy.logerr(f"Failed to initialize Firebase: {str(e)}")
            raise

    def get_navigation_points(self):
        try:
            cpp_ref = db.reference('navigation/coverage_path_planning')
            cpp_data = cpp_ref.get()
            if not cpp_data or not isinstance(cpp_data, dict):
                rospy.logwarn("No coverage_path_planning entries found in Firebase")
                return None
            latest_key = max(cpp_data.keys())
            rospy.loginfo(f"Using latest coverage_path_planning key: {latest_key}")
            ref_path = f'navigation/coverage_path_planning/{latest_key}/waypoints'
            ref = db.reference(ref_path)
            points_data = ref.get()
            points = []
            if isinstance(points_data, dict):
                for point_id, point_data in sorted(points_data.items(), key=lambda x: int(x[0])):
                    try:
                        x = float(point_data.get('x', 0))
                        y = float(point_data.get('y', 0))
                        points.append((x, y))
                    except Exception:
                        pass
            elif isinstance(points_data, list):
                for point_data in points_data:
                    try:
                        x = float(point_data.get('x', 0))
                        y = float(point_data.get('y', 0))
                        points.append((x, y))
                    except Exception:
                        pass
            return points if points else None
        except Exception as e:
            rospy.logerr(f"Error retrieving navigation points: {str(e)}")
            return None

    def send_goal(self, x, y):
        x_transformed = x - self.origin_offset_x
        y_transformed = y - self.origin_offset_y
        goal = MoveBaseGoal()
        goal.target_pose.header.frame_id = "map"
        goal.target_pose.header.stamp = rospy.Time.now()
        goal.target_pose.pose.position = Point(x_transformed, y_transformed, 0)
        goal.target_pose.pose.orientation = Quaternion(0, 0, 0, 1)
        self.move_base_client.send_goal(goal)
        rospy.loginfo(f"Sent goal to position ({x_transformed}, {y_transformed})")
        wait = self.move_base_client.wait_for_result(rospy.Duration(60))
        if not wait:
            rospy.logwarn("Timed out waiting for move_base result")
            self.move_base_client.cancel_goal()
            return False
        return self.move_base_client.get_result()

    # --- Waste Collector methods ---
    def apply_torque(self, link_name, torque):
        try:
            self.clear_wrench(link_name)
            if abs(torque) > 0.001:
                req = ApplyBodyWrenchRequest()
                req.body_name = link_name
                req.reference_frame = "world"
                req.wrench.torque.x = torque
                req.duration = rospy.Duration(-1)
                self.apply_wrench(req)
            # else: just clear
        except rospy.ServiceException as e:
            rospy.logerr(f"Failed to apply torque on {link_name}: {e}")

    def stop_fans(self):
        self.apply_torque(self.fan_right, 0.0)
        self.apply_torque(self.fan_left, 0.0)

    def move_forward_slowly(self):
        self.apply_torque(self.fan_right, self.base_speed)
        self.apply_torque(self.fan_left, self.base_speed)

    def calculate_waste_position_error(self, left_count, right_count, middle_count, bottom_middle_count, upper_left_count=0, upper_right_count=0):
        waste_detected = (left_count > 0 or right_count > 0 or middle_count > 0 or bottom_middle_count > 0 or
                         upper_left_count > 0 or upper_right_count > 0)
        if bottom_middle_count > 0:
            angular_error = 0.0
            linear_error = self.collection_speed
            return angular_error, linear_error
        if middle_count > 0:
            angular_error = 0.0
            linear_error = self.approach_speed
            return angular_error, linear_error
        if waste_detected:
            total_detections = left_count + right_count + upper_left_count + upper_right_count + middle_count + bottom_middle_count
            if total_detections > 0:
                left_ratio = (left_count + upper_left_count) / total_detections
                right_ratio = (right_count + upper_right_count) / total_detections
                center_error = left_ratio - right_ratio
                if left_count > 0 or upper_left_count > 0:
                    if abs(center_error) > self.precision_threshold:
                        angular_error = 0.15 * center_error
                    else:
                        angular_error = 0.05 * center_error
                    linear_error = self.approach_speed
                elif right_count > 0 or upper_right_count > 0:
                    if abs(center_error) > self.precision_threshold:
                        angular_error = 0.15 * center_error
                    else:
                        angular_error = 0.05 * center_error
                    linear_error = self.approach_speed
                else:
                    angular_error = 0.0
                    linear_error = self.approach_speed
            else:
                angular_error = 0.0
                linear_error = self.approach_speed
            return angular_error, linear_error
        angular_error = 0.0
        linear_error = self.search_speed
        return angular_error, linear_error

    def adaptive_pid_controller(self, angular_error, linear_error):
        current_time = time.time()
        dt = current_time - self.previous_time
        if dt <= 0:
            dt = 0.01
        angular_error_abs = abs(angular_error)
        linear_error_abs = abs(linear_error)
        if angular_error_abs > self.max_error_threshold or linear_error_abs > self.max_error_threshold:
            kp_ang, ki_ang, kd_ang = self.kp_angular, self.ki_angular, self.kd_angular
            kp_lin, ki_lin, kd_lin = self.kp_linear, self.ki_linear, self.kd_linear
        elif angular_error_abs < self.fine_error_threshold and linear_error_abs < self.fine_error_threshold:
            kp_ang, ki_ang, kd_ang = self.kp_angular_fine, self.ki_angular_fine, self.kd_angular_fine
            kp_lin, ki_lin, kd_lin = self.kp_linear_fine, self.ki_linear_fine, self.kd_linear_fine
        else:
            kp_ang = (self.kp_angular + self.kp_angular_fine) / 2
            ki_ang = (self.ki_angular + self.ki_angular_fine) / 2
            kd_ang = (self.kd_angular + self.kd_angular_fine) / 2
            kp_lin = (self.kp_linear + self.kp_linear_fine) / 2
            ki_lin = (self.ki_linear + self.ki_linear_fine) / 2
            kd_lin = (self.kd_linear + self.kd_linear_fine) / 2
        self.angular_error_history.pop(0)
        self.angular_error_history.append(angular_error)
        self.linear_error_history.pop(0)
        self.linear_error_history.append(linear_error)
        angular_error_filtered = sum(self.angular_error_history) / len(self.angular_error_history)
        linear_error_filtered = sum(self.linear_error_history) / len(self.linear_error_history)
        angular_p = kp_ang * angular_error_filtered
        self.angular_error_integral += angular_error_filtered * dt
        if abs(self.angular_error_integral) > self.integral_windup_limit:
            self.angular_error_integral = self.integral_windup_limit * (1 if self.angular_error_integral > 0 else -1)
        angular_i = ki_ang * self.angular_error_integral
        angular_derivative_raw = (angular_error_filtered - self.previous_angular_error) / dt
        self.angular_derivative_filtered = (self.derivative_filter_alpha * self.angular_derivative_filtered +
                                          (1 - self.derivative_filter_alpha) * angular_derivative_raw)
        angular_d = kd_ang * self.angular_derivative_filtered
        angular_command = angular_p + angular_i + angular_d
        linear_p = kp_lin * linear_error_filtered
        self.linear_error_integral += linear_error_filtered * dt
        if abs(self.linear_error_integral) > self.integral_windup_limit:
            self.linear_error_integral = self.integral_windup_limit * (1 if self.linear_error_integral > 0 else -1)
        linear_i = ki_lin * self.linear_error_integral
        linear_derivative_raw = (linear_error_filtered - self.previous_linear_error) / dt
        self.linear_derivative_filtered = (self.derivative_filter_alpha * self.linear_derivative_filtered +
                                         (1 - self.derivative_filter_alpha) * linear_derivative_raw)
        linear_d = kd_lin * self.linear_derivative_filtered
        linear_command = linear_p + linear_i + linear_d
        angular_command = max(min(angular_command, self.max_torque), self.min_torque)
        linear_command = max(min(linear_command, self.max_torque), self.min_torque)
        self.previous_angular_error = angular_error_filtered
        self.previous_linear_error = linear_error_filtered
        self.previous_time = current_time
        return angular_command, linear_command

    def apply_differential_control(self, angular_command, linear_command):
        right_motor = self.base_speed + linear_command - angular_command
        left_motor = self.base_speed + linear_command + angular_command
        right_motor = max(min(right_motor, self.max_torque), self.min_torque)
        left_motor = max(min(left_motor, self.max_torque), self.min_torque)
        return right_motor, left_motor

    def regulate_towards_waste(self, left_count, right_count, middle_count, bottom_middle_count, upper_left_count=0, upper_right_count=0):
        if bottom_middle_count >= 1:
            self.move_forward_slowly()
            rospy.sleep(5)
            self.stop_fans()
            rospy.signal_shutdown("Collecte terminée.")
            return True
        angular_error, linear_error = self.calculate_waste_position_error(
            left_count, right_count, middle_count, bottom_middle_count, upper_left_count, upper_right_count
        )
        angular_command, linear_command = self.adaptive_pid_controller(angular_error, linear_error)
        right_motor, left_motor = self.apply_differential_control(angular_command, linear_command)
        self.apply_torque(self.fan_right, right_motor)
        self.apply_torque(self.fan_left, left_motor)
        self.regulation_active = True
        return False

    def reset_pid_integrals(self):
        self.angular_error_integral = 0.0
        self.linear_error_integral = 0.0
        self.previous_angular_error = 0.0
        self.previous_linear_error = 0.0
        self.angular_error_history = [0.0] * 5
        self.linear_error_history = [0.0] * 5
        self.angular_derivative_filtered = 0.0
        self.linear_derivative_filtered = 0.0

    # --- Waste detection callback ---
    def waste_detection_callback(self, msg):
        middle_count = 0
        bottom_middle_count = 0
        left_count = 0
        right_count = 0
        upper_left_count = 0
        upper_right_count = 0
        for section in msg.sections:
            if section.section == "Middle":
                middle_count = section.count
            elif section.section == "Bottom middle":
                bottom_middle_count = section.count
            elif section.section == "Left":
                left_count = section.count
            elif section.section == "Right":
                right_count = section.count
            elif section.section == "Upper left":
                upper_left_count = section.count
            elif section.section == "Upper right":
                upper_right_count = section.count
        waste_detected = (left_count > 0 or right_count > 0 or middle_count > 0 or bottom_middle_count > 0 or upper_left_count > 0 or upper_right_count > 0)
        self.last_detection_msg = (left_count, right_count, middle_count, bottom_middle_count, upper_left_count, upper_right_count)
        if waste_detected:
            self.detection_counter += 1
            if self.detection_counter >= self.detection_threshold:
                self.detection_confirmed = True
                self.last_detection_time = time.time()
                if self.in_search_state:
                    self.in_search_state = False
                    self.search_start_time = None
        else:
            self.detection_counter = 0
            self.detection_confirmed = False
        self.waste_detected = self.detection_confirmed

    def taux_erreur_check(self, event):
        now = time.time()
        if not self.in_search_state:
            if now - self.last_detection_time > self.taux_erreur_period:
                self.in_search_state = True
                self.search_start_time = now
                self.move_forward_slowly()
        else:
            if self.search_start_time and (now - self.search_start_time > self.taux_erreur_period):
                self.stop_fans()
                self.in_search_state = False
                self.search_start_time = None

    # --- Main loop ---
    def run(self):
        rate = rospy.Rate(2)
        while not rospy.is_shutdown():
            now = time.time()
            if self.mode == "NAVIGATION":
                # Navigation autonome tant qu'aucun déchet détecté
                if not self.points:
                    self.points = self.get_navigation_points()
                    self.current_point_idx = 0
                    if not self.points:
                        rospy.loginfo("No navigation points available. Waiting...")
                        rate.sleep()
                        continue
                if self.current_point_idx < len(self.points):
                    x, y = self.points[self.current_point_idx]
                    rospy.loginfo(f"Navigating to point {self.current_point_idx+1}/{len(self.points)}: ({x}, {y})")
                    result = self.send_goal(x, y)
                    if result:
                        rospy.loginfo(f"Reached point {self.current_point_idx+1}")
                        self.current_point_idx += 1
                    else:
                        rospy.logwarn(f"Failed to reach point {self.current_point_idx+1}, retrying...")
                else:
                    rospy.loginfo("Completed all navigation points. Waiting for new points...")
                    self.points = []
                    rospy.sleep(10)
                # Si détection de déchet confirmée, basculer en mode collecte (hors cooldown)
                if self.waste_detected and self.last_detection_msg and (now - self.last_collection_time > self.collection_cooldown):
                    rospy.loginfo("Déchet détecté (confirmé), passage en mode COLLECT.")
                    self.mode = "COLLECT"
                    self.reset_pid_integrals()
            elif self.mode == "COLLECT":
                # Mode collecte IA
                left_count, right_count, middle_count, bottom_middle_count, upper_left_count, upper_right_count = self.last_detection_msg if self.last_detection_msg else (0,0,0,0,0,0)
                if bottom_middle_count >= 1:
                    # Déchet dans Bottom Middle : avancer 5s puis retour navigation
                    if self.bottom_middle_timer is None:
                        rospy.loginfo("Déchet dans Bottom Middle, avancer 5s pour collecte.")
                        self.move_forward_slowly()
                        self.bottom_middle_timer = time.time()
                    elif time.time() - self.bottom_middle_timer >= 5.0:
                        self.stop_fans()
                        rospy.loginfo("Collecte terminée, retour en mode NAVIGATION (cooldown activé).")
                        self.mode = "NAVIGATION"
                        self.bottom_middle_timer = None
                        self.waste_detected = False
                        self.detection_confirmed = False
                        self.detection_counter = 0
                        self.last_detection_msg = None
                        self.collection_count += 1
                        self.last_collection_time = time.time()
                        self.reset_pid_integrals()
                    # Sinon, continuer à avancer
                else:
                    # PID pour fixer le déchet dans Middle puis Bottom Middle
                    angular_error, linear_error = self.calculate_waste_position_error(
                        left_count, right_count, middle_count, bottom_middle_count, upper_left_count, upper_right_count
                    )
                    angular_command, linear_command = self.adaptive_pid_controller(angular_error, linear_error)
                    right_motor, left_motor = self.apply_differential_control(angular_command, linear_command)
                    self.apply_torque(self.fan_right, right_motor)
                    self.apply_torque(self.fan_left, left_motor)
                    self.bottom_middle_timer = None  # reset timer si le déchet quitte Bottom Middle
            rate.sleep()

if __name__ == '__main__':
    try:
        node = NavigationAICollector()
        node.run()
    except rospy.ROSInterruptException:
        pass

