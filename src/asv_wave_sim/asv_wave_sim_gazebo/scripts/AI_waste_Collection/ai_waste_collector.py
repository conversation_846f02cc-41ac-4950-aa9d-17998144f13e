#!/usr/bin/env python3
"""
AI Waste Collection Node for ASV System

This module implements autonomous waste collection behavior:
- Waste detection processing and response
- Coordination with autonomous navigation
- Fan-based propulsion control for collection
- Firebase integration for mode management
- Collection state management with safety timeouts

Author: ASV Team
Version: 2.0
"""

import rospy
from asv_wave_sim_gazebo.msg import WasteDetection
from std_msgs.msg import String, Bool
from gazebo_msgs.srv import ApplyBodyWrench, ApplyBodyWrenchRequest, BodyRequest
import time
import logging
import firebase_admin
from firebase_admin import credentials, firestore
from typing import Dict, Optional
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CollectionState(Enum):
    """Enumeration for collection states"""
    IDLE = "idle"
    COLLECTING = "collecting"
    PAUSED = "paused"
    ERROR = "error"


class WasteCollectionNode:
    """Main class for autonomous waste collection functionality"""

    def __init__(self):
        # Initialize the ROS node
        rospy.init_node('waste_collection_node', anonymous=True)

        # Initialize services with error handling
        self._initialize_gazebo_services()

        # State variables
        self.collection_state = CollectionState.IDLE
        self.last_waste_detection_time = None
        self.waste_detection_timeout = 5.0  # 5 seconds timeout

        # Fan control parameters with safety limits
        self.fan_params = {
            'forward_speed': -0.8,
            'strong_rotation': -0.4,
            'gentle_rotation': -0.2,
            'max_torque': 1.0,
            'min_torque': -1.0
        }

        self.fan_names = {
            'right': "boatcleaningc::fandroit",
            'left': "boatcleaningc::fangauche"
        }

        # Initialize publishers and subscribers
        self._setup_ros_communication()

        # Initialize Firebase
        self._initialize_firebase()

        # Navigation coordination
        self.navigation_active = True
        self.waste_detected = False

        logger.info("Waste Collection Node started successfully")

    def _initialize_gazebo_services(self):
        """Initialize Gazebo service connections with error handling"""
        try:
            rospy.wait_for_service('/gazebo/apply_body_wrench', timeout=10)
            rospy.wait_for_service('/gazebo/clear_body_wrenches', timeout=10)
            self.apply_wrench = rospy.ServiceProxy('/gazebo/apply_body_wrench', ApplyBodyWrench)
            self.clear_wrench = rospy.ServiceProxy('/gazebo/clear_body_wrenches', BodyRequest)
            logger.info("Gazebo services initialized successfully")
        except rospy.ROSException as e:
            logger.error(f"Failed to initialize Gazebo services: {e}")
            raise

    def _setup_ros_communication(self):
        """Setup ROS publishers and subscribers"""
        # Publishers
        self.collection_status_pub = rospy.Publisher('/collection_status', String, queue_size=1)
        self.waste_collection_active_pub = rospy.Publisher('/waste_collection_active', Bool, queue_size=1)
        self.navigation_control_pub = rospy.Publisher('/navigation_control', String, queue_size=1)

        # Subscribers
        rospy.Subscriber('/collection_control', String, self.collection_control_callback)
        rospy.Subscriber('/waste_detection', WasteDetection, self.waste_detection_callback)
        rospy.Subscriber('/navigation_active', Bool, self.navigation_status_callback)

        logger.info("ROS communication setup completed")

    def _initialize_firebase(self):
        """Initialize Firebase connection with error handling"""
        try:
            cred = credentials.Certificate('/home/<USER>/asv_ws/credentials/auth.json')
            try:
                firebase_admin.get_app()
                logger.info("Firebase already initialized")
            except ValueError:
                firebase_admin.initialize_app(cred)
                logger.info("Firebase initialized successfully")
            self.db = firestore.client()
        except Exception as e:
            logger.error(f"Failed to initialize Firebase: {e}")
            self.db = None

    def get_mode_from_firebase(self):
        doc = self.db.collection('asv_control').document('mode').get()
        if doc.exists:
            teleguider = doc.to_dict().get('teleguider', True)
            return 'teleguider' if teleguider else 'autonome'
        return 'teleguider'

    def apply_torque(self, link_name, torque):
        """Apply torque to a specific fan"""
        try:
            self.clear_wrench(link_name)
            if abs(torque) > 0.001:
                req = ApplyBodyWrenchRequest()
                req.body_name = link_name
                req.reference_frame = "world"
                req.wrench.torque.x = torque
                req.duration = rospy.Duration(-1)
                self.apply_wrench(req)
                rospy.loginfo(f"Applied torque {torque} on {link_name}")
        except rospy.ServiceException as e:
            rospy.logerr(f"Failed to apply torque on {link_name}: {e}")

    def stop_fans(self):
        """Stop both fans"""
        self.apply_torque(self.fan_right, 0.0)
        self.apply_torque(self.fan_left, 0.0)
        rospy.loginfo("Boat stopped.")

    def move_forward(self):
        self.stop_fans()
        """Move the boat forward at collection speed"""
        self.apply_torque(self.fan_right, self.forward_speed)
        self.apply_torque(self.fan_left, self.forward_speed)
        rospy.loginfo("Boat moving forward at collection speed.")

    def rotate_left(self, torque):
        self.stop_fans()
        """Rotate the boat left with specified torque"""
        self.apply_torque(self.fan_right,torque )
        self.apply_torque(self.fan_left, 0.0)
        rospy.loginfo(f"Boat rotating left with torque {torque}")
        rospy.sleep(0.5)
        self.stop_fans()

    def rotate_right(self, torque):
        """Rotate the boat right with specified torque"""
        self.apply_torque(self.fan_right, 0.0)
        self.apply_torque(self.fan_left, torque)
        rospy.loginfo(f"Boat rotating right with torque {torque}")
        rospy.sleep(0.5)
        self.stop_fans()

    def control_boat_movement(self, section_counts):
        """Control boat movement based on waste detection sections"""
        if not self.is_collecting:
            self.stop_fans()
            return

        # Check each section and control movement accordingly
        if section_counts["Middle"] > 0 or section_counts["Bottom middle"] > 0:
            self.move_forward()
        elif section_counts["Left"] > 0:
            self.rotate_left(self.strong_rotation)
        elif section_counts["Right"] > 0:
            self.rotate_right(self.strong_rotation)
        elif section_counts["Upper left"] > 0:
            self.rotate_left(self.gentle_rotation)
        elif section_counts["Upper right"] > 0:
            self.rotate_right(self.gentle_rotation)
        elif section_counts["Bottom middle"] > 0:
            self.move_forward()
        else:
            self.stop_fans()

    def collection_control_callback(self, msg):
        """Callback function for collection control messages"""
        command = msg.data
        rospy.loginfo(f"[ Controller ]==========>: {command}")
        
        if command == 'start_collection':
            self.start_collection()
        elif command == 'end_collection':
            self.pause_collection()

    def navigation_status_callback(self, msg):
        """Handle navigation status updates"""
        self.navigation_active = msg.data
        logger.debug(f"Navigation status: {'active' if self.navigation_active else 'inactive'}")

    def activate_waste_collection(self):
        """Activate waste collection and pause navigation"""
        if self.navigation_active:
            logger.info("Activating waste collection - pausing navigation")
            self.navigation_control_pub.publish(String(data='pause'))

        self.collection_state = CollectionState.COLLECTING
        self.waste_collection_active_pub.publish(Bool(data=True))
        self.last_waste_detection_time = time.time()
        self.collection_status_pub.publish(String(data=self.collection_state.value))
        logger.info("Waste collection activated")

    def deactivate_waste_collection(self):
        """Deactivate waste collection and resume navigation"""
        self.collection_state = CollectionState.PAUSED
        self.last_waste_detection_time = None
        self.waste_collection_active_pub.publish(Bool(data=False))
        self.collection_status_pub.publish(String(data=self.collection_state.value))
        self.stop_fans()

        # Resume navigation
        logger.info("Deactivating waste collection - resuming navigation")
        self.navigation_control_pub.publish(String(data='activate'))
        logger.info("Waste collection deactivated")

    def start_collection(self):
        """Start the collection process (legacy method for compatibility)"""
        self.activate_waste_collection()

    def pause_collection(self):
        """Pause the collection process (legacy method for compatibility)"""
        self.deactivate_waste_collection()

    def check_waste_detection_timeout(self) -> bool:
        """Check if waste detection has timed out"""
        if (self.collection_state != CollectionState.COLLECTING or
            self.last_waste_detection_time is None):
            return False

        current_time = time.time()
        if current_time - self.last_waste_detection_time > self.waste_detection_timeout:
            logger.info(f"No waste detected for {self.waste_detection_timeout} seconds, deactivating collection")
            self.deactivate_waste_collection()
            return True
        return False

    def waste_detection_callback(self, msg):
        """Callback function for waste detection messages"""
        # Always process waste detection to potentially activate collection
        # Initialize section counts with default values

        section_counts = {
            "Left": 0,
            "Right": 0,
            "Upper left": 0,
            "Upper right": 0,
            "Middle": 0,
            "Bottom middle": 0
        }

        # Update counts from message
        for section in msg.sections:
            if section.section in section_counts:
                section_counts[section.section] = section.count
            else:
                logger.warning(f"Unknown section detected: {section.section}")

        # Check if any waste is detected
        total_waste = sum(section_counts.values())

        if total_waste > 0:
            # Waste detected - activate collection if not already active
            if self.collection_state != CollectionState.COLLECTING:
                logger.info("Waste detected - activating collection mode")
                self.activate_waste_collection()

            # Update detection time and control movement
            self.last_waste_detection_time = time.time()
            self.control_boat_movement(section_counts)

            # Log detection info (optional, can be disabled for performance)
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug("=== Waste Detection Counts ===")
                for section, count in section_counts.items():
                    if count > 0:
                        logger.debug(f"{section}: {count}")
                logger.debug("============================")
        else:
            # No waste detected - check for timeout if currently collecting
            if self.collection_state == CollectionState.COLLECTING:
                self.check_waste_detection_timeout()

        return section_counts

    def run(self):
        """Main execution loop"""
        rate = rospy.Rate(10)  # 10 Hz
        last_mode = None

        logger.info("Starting AI waste collector main loop")

        # Initially activate navigation
        self.navigation_control_pub.publish(String(data='activate'))

        while not rospy.is_shutdown():
            try:
                mode = self.get_mode_from_firebase()

                # Log mode changes
                if mode != last_mode:
                    logger.info(f"Mode changed to: {mode}")
                    last_mode = mode

                if mode == 'autonome':
                    # Autonomous mode: allow AI waste collection
                    if self.collection_state == CollectionState.COLLECTING:
                        self.check_waste_detection_timeout()
                else:
                    # Manual mode: stop collection and ensure navigation is paused
                    if self.collection_state == CollectionState.COLLECTING:
                        logger.info("Switching to manual mode, deactivating waste collection")
                        self.deactivate_waste_collection()

                    # In manual mode, ensure navigation is paused for manual control
                    self.navigation_control_pub.publish(String(data='pause'))
                    self.stop_fans()  # Always stop fans in manual mode

            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                self.collection_state = CollectionState.ERROR
                self.stop_fans()

            rate.sleep()

if __name__ == '__main__':
    try:
        node = WasteCollectionNode()
        node.run()
    except rospy.ROSInterruptException:
        pass