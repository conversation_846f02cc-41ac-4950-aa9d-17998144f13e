#!/usr/bin/env python3
"""
Test script for AI Waste Collection and Navigation Coordination

This script tests the communication between the navigation node and AI waste collector.
It simulates waste detection events to verify proper coordination.

Author: ASV Team
Version: 1.0
"""

import rospy
from std_msgs.msg import String, Bool
from asv_wave_sim_gazebo.msg import WasteDetection, WasteSection
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CoordinationTester:
    """Test class for coordination between navigation and waste collection"""
    
    def __init__(self):
        rospy.init_node('coordination_tester', anonymous=True)
        
        # Publishers for testing
        self.waste_detection_pub = rospy.Publisher('/waste_detection', WasteDetection, queue_size=1)
        self.collection_control_pub = rospy.Publisher('/collection_control', String, queue_size=1)
        
        # Subscribers to monitor system state
        rospy.Subscriber('/navigation_status', String, self.navigation_status_callback)
        rospy.Subscriber('/collection_status', String, self.collection_status_callback)
        rospy.Subscriber('/navigation_active', Bool, self.navigation_active_callback)
        rospy.Subscriber('/waste_collection_active', Bool, self.waste_collection_active_callback)
        
        # State tracking
        self.navigation_status = "unknown"
        self.collection_status = "unknown"
        self.navigation_active = False
        self.waste_collection_active = False
        
        logger.info("Coordination Tester initialized")

    def navigation_status_callback(self, msg):
        """Monitor navigation status"""
        self.navigation_status = msg.data
        logger.info(f"Navigation Status: {self.navigation_status}")

    def collection_status_callback(self, msg):
        """Monitor collection status"""
        self.collection_status = msg.data
        logger.info(f"Collection Status: {self.collection_status}")

    def navigation_active_callback(self, msg):
        """Monitor navigation active state"""
        self.navigation_active = msg.data
        logger.info(f"Navigation Active: {self.navigation_active}")

    def waste_collection_active_callback(self, msg):
        """Monitor waste collection active state"""
        self.waste_collection_active = msg.data
        logger.info(f"Waste Collection Active: {self.waste_collection_active}")

    def create_waste_detection_message(self, sections_data):
        """Create a waste detection message with specified sections"""
        msg = WasteDetection()
        msg.header.stamp = rospy.Time.now()
        msg.header.frame_id = "camera_frame"
        
        for section_name, count in sections_data.items():
            section = WasteSection()
            section.section = section_name
            section.count = count
            msg.sections.append(section)
        
        return msg

    def test_waste_detection_activation(self):
        """Test that waste detection activates collection and pauses navigation"""
        logger.info("=== Testing Waste Detection Activation ===")
        
        # Wait for system to stabilize
        rospy.sleep(2.0)
        
        # Send waste detection with waste in middle section
        waste_data = {
            "Middle": 3,
            "Left": 0,
            "Right": 0,
            "Upper left": 0,
            "Upper right": 0,
            "Bottom middle": 1
        }
        
        msg = self.create_waste_detection_message(waste_data)
        logger.info("Sending waste detection message...")
        self.waste_detection_pub.publish(msg)
        
        # Wait and check results
        rospy.sleep(3.0)
        
        # Verify expected behavior
        if self.waste_collection_active and not self.navigation_active:
            logger.info("✓ SUCCESS: Waste collection activated, navigation paused")
        else:
            logger.error("✗ FAILURE: Expected waste collection active and navigation paused")
            logger.error(f"  Waste collection active: {self.waste_collection_active}")
            logger.error(f"  Navigation active: {self.navigation_active}")

    def test_no_waste_timeout(self):
        """Test that no waste detection times out and resumes navigation"""
        logger.info("=== Testing No Waste Timeout ===")
        
        # Send empty waste detection messages
        empty_waste_data = {
            "Middle": 0,
            "Left": 0,
            "Right": 0,
            "Upper left": 0,
            "Upper right": 0,
            "Bottom middle": 0
        }
        
        # Send several empty detections
        for i in range(10):
            msg = self.create_waste_detection_message(empty_waste_data)
            self.waste_detection_pub.publish(msg)
            rospy.sleep(0.5)
        
        # Wait for timeout (should be 5 seconds)
        logger.info("Waiting for timeout...")
        rospy.sleep(6.0)
        
        # Verify expected behavior
        if not self.waste_collection_active and self.navigation_active:
            logger.info("✓ SUCCESS: Waste collection timed out, navigation resumed")
        else:
            logger.error("✗ FAILURE: Expected waste collection inactive and navigation active")
            logger.error(f"  Waste collection active: {self.waste_collection_active}")
            logger.error(f"  Navigation active: {self.navigation_active}")

    def test_manual_control(self):
        """Test manual collection control commands"""
        logger.info("=== Testing Manual Control ===")
        
        # Test start collection command
        logger.info("Sending start collection command...")
        self.collection_control_pub.publish(String(data='start_collection'))
        rospy.sleep(2.0)
        
        if self.waste_collection_active:
            logger.info("✓ SUCCESS: Manual start collection worked")
        else:
            logger.error("✗ FAILURE: Manual start collection failed")
        
        # Test end collection command
        logger.info("Sending end collection command...")
        self.collection_control_pub.publish(String(data='end_collection'))
        rospy.sleep(2.0)
        
        if not self.waste_collection_active and self.navigation_active:
            logger.info("✓ SUCCESS: Manual end collection worked")
        else:
            logger.error("✗ FAILURE: Manual end collection failed")

    def print_system_status(self):
        """Print current system status"""
        logger.info("=== Current System Status ===")
        logger.info(f"Navigation Status: {self.navigation_status}")
        logger.info(f"Collection Status: {self.collection_status}")
        logger.info(f"Navigation Active: {self.navigation_active}")
        logger.info(f"Waste Collection Active: {self.waste_collection_active}")
        logger.info("=============================")

    def run_tests(self):
        """Run all coordination tests"""
        logger.info("Starting coordination tests...")
        
        # Wait for nodes to start up
        logger.info("Waiting for system startup...")
        rospy.sleep(5.0)
        
        self.print_system_status()
        
        # Run tests
        self.test_waste_detection_activation()
        rospy.sleep(2.0)
        
        self.test_no_waste_timeout()
        rospy.sleep(2.0)
        
        self.test_manual_control()
        rospy.sleep(2.0)
        
        self.print_system_status()
        logger.info("All tests completed!")


def main():
    """Main entry point"""
    try:
        tester = CoordinationTester()
        tester.run_tests()
    except rospy.ROSInterruptException:
        logger.info("Test interrupted")
    except Exception as e:
        logger.error(f"Test error: {e}")


if __name__ == '__main__':
    main()
