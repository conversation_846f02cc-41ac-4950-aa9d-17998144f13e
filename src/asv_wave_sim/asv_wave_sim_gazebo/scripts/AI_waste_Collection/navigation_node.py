#!/usr/bin/env python3
"""
Autonomous Navigation Node for ASV System

This node handles continuous autonomous navigation when no waste collection is active.
It communicates with the AI waste collector to coordinate control handover.

Author: ASV Team
Version: 1.0
"""

import rospy
from std_msgs.msg import String, Bool
from gazebo_msgs.srv import Apply<PERSON>ody<PERSON><PERSON>, ApplyBodyWrenchRequest, BodyRequest
import time
import logging
from enum import Enum
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NavigationState(Enum):
    """Navigation states"""
    ACTIVE = "active"
    PAUSED = "paused"
    DISABLED = "disabled"


class AutonomousNavigationNode:
    """Main class for autonomous navigation functionality"""
    
    def __init__(self):
        # Initialize ROS node
        rospy.init_node('autonomous_navigation_node', anonymous=True)
        
        # Initialize Gazebo services
        self._initialize_gazebo_services()
        
        # Navigation state
        self.navigation_state = NavigationState.ACTIVE
        self.last_navigation_time = time.time()
        
        # Navigation parameters
        self.nav_params = {
            'cruise_speed': -0.4,  # Forward speed for cruising
            'turn_speed': -0.3,    # Speed for turning
            'pattern_duration': 10.0,  # Time for each navigation pattern
            'max_torque': 1.0,
            'min_torque': -1.0
        }
        
        # Fan names
        self.fan_names = {
            'right': "boatcleaningc::fandroit",
            'left': "boatcleaningc::fangauche"
        }
        
        # Navigation pattern state
        self.current_pattern_step = 0
        self.pattern_start_time = time.time()
        
        # Setup ROS communication
        self._setup_ros_communication()
        
        logger.info("Autonomous Navigation Node initialized successfully")

    def _initialize_gazebo_services(self):
        """Initialize Gazebo service connections"""
        try:
            rospy.wait_for_service('/gazebo/apply_body_wrench', timeout=10)
            rospy.wait_for_service('/gazebo/clear_body_wrenches', timeout=10)
            self.apply_wrench = rospy.ServiceProxy('/gazebo/apply_body_wrench', ApplyBodyWrench)
            self.clear_wrench = rospy.ServiceProxy('/gazebo/clear_body_wrenches', BodyRequest)
            logger.info("Gazebo services initialized successfully")
        except rospy.ROSException as e:
            logger.error(f"Failed to initialize Gazebo services: {e}")
            raise

    def _setup_ros_communication(self):
        """Setup ROS publishers and subscribers"""
        # Publishers
        self.nav_status_pub = rospy.Publisher('/navigation_status', String, queue_size=1)
        self.nav_active_pub = rospy.Publisher('/navigation_active', Bool, queue_size=1)
        
        # Subscribers
        rospy.Subscriber('/navigation_control', String, self.navigation_control_callback)
        rospy.Subscriber('/waste_collection_active', Bool, self.waste_collection_callback)
        
        logger.info("ROS communication setup completed")

    def apply_torque(self, link_name: str, torque: float) -> bool:
        """Apply torque to a specific fan with safety limits"""
        try:
            # Apply safety limits
            torque = max(min(torque, self.nav_params['max_torque']), 
                        self.nav_params['min_torque'])
            
            self.clear_wrench(link_name)
            if abs(torque) > 0.001:
                req = ApplyBodyWrenchRequest()
                req.body_name = link_name
                req.reference_frame = "world"
                req.wrench.torque.x = torque
                req.duration = rospy.Duration(-1)
                self.apply_wrench(req)
                logger.debug(f"Applied navigation torque {torque:.2f} on {link_name}")
            return True
        except rospy.ServiceException as e:
            logger.error(f"Failed to apply torque on {link_name}: {e}")
            return False

    def stop_navigation(self):
        """Stop navigation movement"""
        self.apply_torque(self.fan_names['right'], 0.0)
        self.apply_torque(self.fan_names['left'], 0.0)
        logger.debug("Navigation stopped")

    def execute_navigation_pattern(self):
        """Execute autonomous navigation pattern"""
        if self.navigation_state != NavigationState.ACTIVE:
            self.stop_navigation()
            return

        current_time = time.time()
        pattern_elapsed = current_time - self.pattern_start_time
        
        # Simple navigation pattern: forward -> turn right -> forward -> turn left
        pattern_cycle = self.nav_params['pattern_duration']
        phase = (pattern_elapsed % (pattern_cycle * 4)) / pattern_cycle
        
        if phase < 1.0:  # Forward movement
            self.move_forward()
        elif phase < 2.0:  # Turn right
            self.turn_right()
        elif phase < 3.0:  # Forward movement
            self.move_forward()
        else:  # Turn left
            self.turn_left()

    def move_forward(self):
        """Move forward for navigation"""
        speed = self.nav_params['cruise_speed']
        self.apply_torque(self.fan_names['right'], speed)
        self.apply_torque(self.fan_names['left'], speed)

    def turn_right(self):
        """Turn right for navigation"""
        turn_speed = self.nav_params['turn_speed']
        self.apply_torque(self.fan_names['right'], 0.0)
        self.apply_torque(self.fan_names['left'], turn_speed)

    def turn_left(self):
        """Turn left for navigation"""
        turn_speed = self.nav_params['turn_speed']
        self.apply_torque(self.fan_names['right'], turn_speed)
        self.apply_torque(self.fan_names['left'], 0.0)

    def navigation_control_callback(self, msg):
        """Handle navigation control commands"""
        command = msg.data
        logger.info(f"Navigation control command: {command}")
        
        if command == 'activate':
            self.activate_navigation()
        elif command == 'pause':
            self.pause_navigation()
        elif command == 'disable':
            self.disable_navigation()
        else:
            logger.warning(f"Unknown navigation command: {command}")

    def waste_collection_callback(self, msg):
        """Handle waste collection status updates"""
        collection_active = msg.data
        
        if collection_active:
            logger.info("Waste collection activated - pausing navigation")
            self.pause_navigation()
        else:
            logger.info("Waste collection deactivated - resuming navigation")
            self.activate_navigation()

    def activate_navigation(self):
        """Activate autonomous navigation"""
        self.navigation_state = NavigationState.ACTIVE
        self.pattern_start_time = time.time()
        self.nav_status_pub.publish(String(data=self.navigation_state.value))
        self.nav_active_pub.publish(Bool(data=True))
        logger.info("Navigation activated")

    def pause_navigation(self):
        """Pause navigation (can be resumed)"""
        self.navigation_state = NavigationState.PAUSED
        self.stop_navigation()
        self.nav_status_pub.publish(String(data=self.navigation_state.value))
        self.nav_active_pub.publish(Bool(data=False))
        logger.info("Navigation paused")

    def disable_navigation(self):
        """Disable navigation completely"""
        self.navigation_state = NavigationState.DISABLED
        self.stop_navigation()
        self.nav_status_pub.publish(String(data=self.navigation_state.value))
        self.nav_active_pub.publish(Bool(data=False))
        logger.info("Navigation disabled")

    def run(self):
        """Main execution loop"""
        rate = rospy.Rate(10)  # 10 Hz
        logger.info("Starting navigation main loop")
        
        while not rospy.is_shutdown():
            try:
                # Execute navigation if active
                if self.navigation_state == NavigationState.ACTIVE:
                    self.execute_navigation_pattern()
                
                # Publish status periodically
                self.nav_status_pub.publish(String(data=self.navigation_state.value))
                self.nav_active_pub.publish(Bool(data=self.navigation_state == NavigationState.ACTIVE))
                
            except Exception as e:
                logger.error(f"Error in navigation loop: {e}")
                self.pause_navigation()
                
            rate.sleep()


def main():
    """Main entry point"""
    try:
        nav_node = AutonomousNavigationNode()
        nav_node.run()
    except rospy.ROSInterruptException:
        logger.info("Navigation node shutdown requested")
    except Exception as e:
        logger.error(f"Navigation node error: {e}")
    finally:
        logger.info("Navigation node shutdown complete")


if __name__ == '__main__':
    main()
