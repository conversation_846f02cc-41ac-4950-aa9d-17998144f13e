#!/usr/bin/env python3
"""
Web Control Stream Node for ASV Waste Collection System

This module provides a Flask web interface for:
- Live camera streaming from ROS
- Manual control of the ASV boat
- Mode switching between autonomous and manual control
- Firebase integration for persistent mode storage

Author: ASV Team
Version: 2.0
"""

import rospy
from flask import Flask, Response, request, jsonify
from flask_cors import CORS
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
from gazebo_msgs.srv import Apply<PERSON><PERSON><PERSON><PERSON>, ApplyBodyWrenchRequest, BodyRequest
import cv2
import threading
import time
import signal
import socket
import logging
import firebase_admin
from firebase_admin import credentials, firestore

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebControlStreamNode:
    """Main class for web control and streaming functionality"""

    def __init__(self):
        # Flask app setup
        self.app = Flask(__name__)
        CORS(self.app)

        # ROS components
        self.bridge = CvBridge()
        self.latest_frame = None
        self.ros_initialized = False
        self.shutdown_flag = False

        # Control parameters
        self.fan_torque_limits = {'min': -1.0, 'max': 1.0}
        self.fan_names = {
            'right': "boatcleaningc::fandroit",
            'left': "boatcleaningc::fangauche"
        }

        # Initialize Firebase
        self._initialize_firebase()

        # Setup Flask routes
        self._setup_routes()

    def _initialize_firebase(self):
        """Initialize Firebase connection with error handling"""
        try:
            cred = credentials.Certificate('/home/<USER>/asv_ws/credentials/auth.json')
            # Check if Firebase is already initialized
            try:
                firebase_admin.get_app()
                logger.info("Firebase already initialized")
            except ValueError:
                firebase_admin.initialize_app(cred)
                logger.info("Firebase initialized successfully")
            self.db = firestore.client()
        except Exception as e:
            logger.error(f"Failed to initialize Firebase: {e}")
            self.db = None

    def _setup_routes(self):
        """Setup Flask routes"""
        self.app.route('/video_feed')(self.video_feed)
        self.app.route('/control', methods=['POST'])(self.control)
        self.app.route('/get_mode', methods=['GET'])(self.get_mode)
        self.app.route('/set_mode', methods=['POST'])(self.set_mode)
        self.app.route('/')(self.index)

    def get_ip_address(self) -> str:
        """Get local IP address for interface display"""
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        try:
            s.connect(('**************', 1))
            ip = s.getsockname()[0]
        except Exception:
            ip = '127.0.0.1'
        finally:
            s.close()
        return ip

    def image_callback(self, msg):
        """ROS callback to receive camera images and convert to JPEG"""
        try:
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            _, jpeg = cv2.imencode('.jpg', cv_image, [cv2.IMWRITE_JPEG_QUALITY, 80])
            self.latest_frame = jpeg.tobytes()
        except Exception as e:
            logger.error(f"Image processing error: {e}")

    def gen_frames(self):
        """Generator for MJPEG video stream"""
        while not self.shutdown_flag:
            if self.latest_frame:
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + self.latest_frame + b'\r\n')
            time.sleep(0.033)

    def video_feed(self):
        """Flask route for video stream"""
        return Response(self.gen_frames(),
                        mimetype='multipart/x-mixed-replace; boundary=frame')

    def apply_torque(self, link_name: str, torque: float) -> bool:
        """Apply torque to a specific fan with safety limits"""
        try:
            # Apply safety limits
            torque = max(min(torque, self.fan_torque_limits['max']),
                        self.fan_torque_limits['min'])

            rospy.wait_for_service('/gazebo/apply_body_wrench')
            rospy.wait_for_service('/gazebo/clear_body_wrenches')
            clear_wrench = rospy.ServiceProxy('/gazebo/clear_body_wrenches', BodyRequest)
            apply_wrench = rospy.ServiceProxy('/gazebo/apply_body_wrench', ApplyBodyWrench)

            clear_wrench(link_name)

            if abs(torque) > 0.001:
                req = ApplyBodyWrenchRequest()
                req.body_name = link_name
                req.reference_frame = "world"
                req.wrench.torque.x = torque
                req.duration = rospy.Duration(-1)  # Continuous application
                apply_wrench(req)
                logger.info(f"Applied torque {torque:.2f} Nm to {link_name}")
            else:
                logger.info(f"Stopped torque on {link_name}")
            return True
        except rospy.ServiceException as e:
            logger.error(f"Failed to apply torque on {link_name}: {e}")
            return False

    def get_mode_from_firebase(self) -> bool:
        """Get current mode from Firebase (True = teleguider, False = autonome)"""
        if not self.db:
            logger.warning("Firebase not available, defaulting to teleguider mode")
            return True

        try:
            doc = self.db.collection('asv_control').document('mode').get()
            if doc.exists:
                return doc.to_dict().get('teleguider', True)
        except Exception as e:
            logger.error(f"Failed to get mode from Firebase: {e}")
        return True  # Default to teleguider mode

    def set_mode_in_firebase(self, mode: str) -> bool:
        """Set mode in Firebase"""
        if not self.db:
            logger.warning("Firebase not available, cannot set mode")
            return False

        try:
            self.db.collection('asv_control').document('mode').set({
                'teleguider': mode == 'teleguider'
            })
            logger.info(f"Mode set to: {mode}")
            return True
        except Exception as e:
            logger.error(f"Failed to set mode in Firebase: {e}")
            return False

    def control(self):
        """Flask route for manual boat control"""
        mode = self.get_mode_from_firebase()
        if not mode:  # autonome mode
            logger.warning("Manual control attempted in autonome mode")
            # Stop fans if command received in autonomous mode
            self.apply_torque(self.fan_names['right'], 0.0)
            self.apply_torque(self.fan_names['left'], 0.0)
            return jsonify({
                'status': 'error',
                'msg': 'Manual control disabled in autonome mode'
            }), 403

        data = request.get_json()
        cmd = data.get('command', '')

        # Manual commands from web interface
        command_map = {
            'forward': (-0.30, -0.30),
            'backward': (0.30, 0.30),
            'left': (-0.18, 0.5),
            'right': (0.5, -0.18),
            'stop': (0.0, 0.0)
        }

        if cmd in command_map:
            right_torque, left_torque = command_map[cmd]
            self.apply_torque(self.fan_names['right'], right_torque)
            self.apply_torque(self.fan_names['left'], left_torque)
            return jsonify({'status': 'ok'})
        else:
            return jsonify({'status': 'error', 'msg': 'Unknown command'}), 400

    def get_mode(self):
        """Flask route to get current mode"""
        teleguider = self.get_mode_from_firebase()
        mode = 'teleguider' if teleguider else 'autonome'
        return jsonify({'mode': mode})

    def set_mode(self):
        """Flask route to set mode"""
        data = request.get_json()
        mode = data.get('mode', 'teleguider')
        success = self.set_mode_in_firebase(mode)
        if success:
            return jsonify({'status': 'ok', 'mode': mode})
        else:
            return jsonify({'status': 'error', 'msg': 'Failed to set mode'}), 500

    def index(self):
        """Flask route for main web interface"""
        ip = self.get_ip_address()
        return f"""
    <html>
      <head>
        <title>ROS Camera Stream & Control</title>
        <style>
            body {{ font-family: Arial, sans-serif; text-align: center; }}
            .controls button {{
                font-size: 1.5em; margin: 10px; padding: 15px 30px;
            }}
            .mode-btn {{
                font-size: 1.2em; margin: 10px; padding: 10px 20px;
            }}
        </style>
        <script>
        let currentMode = 'teleguider';
        function sendCommand(cmd) {{
            fetch('/control', {{
                method: 'POST',
                headers: {{'Content-Type': 'application/json'}},
                body: JSON.stringify({{command: cmd}})
            }});
        }}
        function setMode(mode) {{
            fetch('/set_mode', {{
                method: 'POST',
                headers: {{'Content-Type': 'application/json'}},
                body: JSON.stringify({{mode: mode}})
            }}).then(r => r.json()).then(data => {{
                currentMode = data.mode;
                updateUI();
            }});
        }}
        function updateUI() {{
            if (currentMode === 'teleguider') {{
                document.getElementById('controls').style.display = 'block';
            }} else {{
                document.getElementById('controls').style.display = 'none';
            }}
            document.getElementById('mode-label').innerText = 
                currentMode === 'teleguider' ? 'Mode Téléguider' : 'Mode Autonome';
        }}
        window.onload = function() {{
            fetch('/get_mode').then(r => r.json()).then(data => {{
                currentMode = data.mode;
                updateUI();
            }});
        }}
        </script>
      </head>
      <body>
        <h1>Live Camera Feed & Manual Control</h1>
        <p>Stream URL: http://{ip}:8080/video_feed</p>
        <div>
            <button class="mode-btn" onclick="setMode('teleguider')">Mode Téléguider</button>
            <button class="mode-btn" onclick="setMode('autonome')">Mode Autonome</button>
        </div>
        <h2 id="mode-label"></h2>
        <img src="/video_feed" width="640" height="360"><br>
        <div id="controls" class="controls">
            <button onclick="sendCommand('forward')">⬆️ Avancer</button><br>
            <button onclick="sendCommand('left')">⬅️ Gauche</button>
            <button onclick="sendCommand('stop')">⏹️ Stop</button>
            <button onclick="sendCommand('right')">➡️ Droite</button><br>
            <button onclick="sendCommand('backward')">⬇️ Reculer</button>
        </div>
      </body>
    </html>
    """

    def initialize_ros(self):
        """Initialize ROS components"""
        try:
            rospy.init_node('web_control_stream', anonymous=True)
            rospy.Subscriber("/camera/image_raw", Image, self.image_callback)
            self.ros_initialized = True
            logger.info("ROS components initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize ROS: {e}")
            self.ros_initialized = False

    def ros_spin(self):
        """ROS spinning thread for receiving camera images"""
        self.ros_initialized = True
        while not self.shutdown_flag and not rospy.is_shutdown():
            rospy.sleep(0.1)

    def signal_handler(self, sig, frame):
        """Handle shutdown signals gracefully"""
        logger.info("Shutting down server...")
        self.shutdown_flag = True
        if self.ros_initialized:
            rospy.signal_shutdown("Server shutdown")

    def run_server(self, port: int = 8080):
        """Run the Flask web server"""
        try:
            # Initialize ROS
            self.initialize_ros()

            # Start ROS thread
            ros_thread = threading.Thread(target=self.ros_spin)
            ros_thread.daemon = True
            ros_thread.start()

            # Wait for ROS to be ready
            while not self.ros_initialized and not rospy.is_shutdown():
                time.sleep(0.1)

            # Setup signal handlers
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)

            # Get IP and start server
            ip_address = self.get_ip_address()
            logger.info("="*50)
            logger.info("Web streaming & control server is running!")
            logger.info(f"Local access: http://localhost:{port}")
            logger.info(f"Network access: http://{ip_address}:{port}")
            logger.info("Press Ctrl+C to stop the server")
            logger.info("="*50)

            self.app.run(host='0.0.0.0', port=port, debug=False,
                        threaded=True, use_reloader=False)

        except KeyboardInterrupt:
            self.signal_handler(None, None)
        except Exception as e:
            logger.error(f"Server error: {e}")
        finally:
            logger.info("Server stopped.")


def main():
    """Main entry point"""
    try:
        web_node = WebControlStreamNode()
        web_node.run_server()
    except Exception as e:
        logger.error(f"Failed to start web control stream: {e}")


if __name__ == '__main__':
    main()

