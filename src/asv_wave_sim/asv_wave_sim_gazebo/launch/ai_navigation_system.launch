<?xml version="1.0"?>
<launch>
    <!-- AI Navigation System Launch File -->
    <!-- This launch file starts both the autonomous navigation and AI waste collection nodes -->
    
    <!-- Parameters -->
    <param name="use_sim_time" value="true"/>
    
    <!-- Autonomous Navigation Node -->
    <node name="autonomous_navigation_node" 
          pkg="asv_wave_sim_gazebo" 
          type="navigation_node.py" 
          output="screen"
          respawn="true"
          respawn_delay="5">
        <remap from="/navigation_status" to="/navigation_status"/>
        <remap from="/navigation_active" to="/navigation_active"/>
        <remap from="/navigation_control" to="/navigation_control"/>
        <remap from="/waste_collection_active" to="/waste_collection_active"/>
    </node>
    
    <!-- AI Waste Collection Node -->
    <node name="ai_waste_collector_node" 
          pkg="asv_wave_sim_gazebo" 
          type="ai_waste_collector.py" 
          output="screen"
          respawn="true"
          respawn_delay="5">
        <remap from="/collection_status" to="/collection_status"/>
        <remap from="/collection_control" to="/collection_control"/>
        <remap from="/waste_detection" to="/waste_detection"/>
        <remap from="/waste_collection_active" to="/waste_collection_active"/>
        <remap from="/navigation_control" to="/navigation_control"/>
        <remap from="/navigation_active" to="/navigation_active"/>
    </node>
    
    <!-- Optional: RQT Graph for visualization -->
    <!-- Uncomment the line below to visualize the node graph -->
    <!-- <node name="rqt_graph" pkg="rqt_graph" type="rqt_graph" /> -->
    
</launch>
