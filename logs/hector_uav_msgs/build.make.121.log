[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_EnableMotors
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_TakeoffAction
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_PoseFeedback
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_TakeoffFeedback
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_TakeoffActionFeedback
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_TakeoffActionGoal
[  0%] Built target std_msgs_generate_messages_nodejs
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_AttitudeCommand
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_TakeoffGoal
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_RawRC
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_RC
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_HeadingCommand
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_PoseAction
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_ControllerState
[  0%] Built target geometry_msgs_generate_messages_nodejs
[  0%] Built target actionlib_msgs_generate_messages_nodejs
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_YawrateCommand
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_PoseActionGoal
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_RawMagnetic
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_Altimeter
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_ServoCommand
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_HeightCommand
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_RawImu
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_PoseResult
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_Compass
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_PoseActionResult
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_TakeoffResult
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_PoseActionFeedback
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_VelocityZCommand
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_LandingActionFeedback
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_PoseGoal
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_MotorStatus
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_PositionXYCommand
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_LandingFeedback
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_RuddersCommand
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_TakeoffActionResult
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_MotorPWM
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_LandingActionGoal
[  0%] Built target geometry_msgs_generate_messages_lisp
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_Supply
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_ThrustCommand
[  0%] Built target std_msgs_generate_messages_lisp
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_LandingResult
[  0%] Built target actionlib_msgs_generate_messages_eus
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target actionlib_msgs_generate_messages_lisp
[  0%] Built target actionlib_msgs_generate_messages_cpp
[  0%] Built target std_msgs_generate_messages_eus
[  0%] Built target geometry_msgs_generate_messages_cpp
[  0%] Built target std_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_py
[  0%] Built target std_msgs_generate_messages_py
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_LandingGoal
[  0%] Built target actionlib_msgs_generate_messages_py
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_LandingActionResult
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_VelocityXYCommand
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_MotorCommand
[  0%] Built target _hector_uav_msgs_generate_messages_check_deps_LandingAction
[ 19%] Built target hector_uav_msgs_generate_messages_nodejs
[ 45%] Built target hector_uav_msgs_generate_messages_lisp
[ 58%] Built target hector_uav_msgs_generate_messages_cpp
[ 79%] Built target hector_uav_msgs_generate_messages_eus
[100%] Built target hector_uav_msgs_generate_messages_py
[100%] Built target hector_uav_msgs_generate_messages
